-- =====================================================
-- GYM PROJECT - ENTİTY DÜZELTME MİGRATİON SCRIPT'LERİ
-- Tarih: 2025-01-13
-- Açıklama: Eksik IsActive, CreationDate, UpdatedDate, DeletedDate alanlarının eklenmesi
-- =====================================================

-- 1. CITY TABLOSU - Eksik alanlar ekleniyor
ALTER TABLE Cities 
ADD IsActive BIT NULL,
    CreationDate DATETIME2 NULL,
    UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 2. TOWN TABLOSU - Eksik alanlar ekleniyor  
ALTER TABLE Towns
ADD IsActive BIT NULL,
    CreationDate DATETIME2 NULL,
    UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 3. DEBTPAYMENT TABLOSU - Nullable düzeltmeleri ve eksik alanlar
ALTER TABLE DebtPayments
ALTER COLUMN IsActive BIT NULL;

-- CreationDate için önce default constraint'i bul ve sil
DECLARE @ConstraintName NVARCHAR(200)
SELECT @ConstraintName = name FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('DebtPayments')
AND parent_column_id = (SELECT column_id FROM sys.columns
                        WHERE object_id = OBJECT_ID('DebtPayments')
                        AND name = 'CreationDate')

IF @ConstraintName IS NOT NULL
    EXEC('ALTER TABLE DebtPayments DROP CONSTRAINT ' + @ConstraintName)

ALTER TABLE DebtPayments
ALTER COLUMN CreationDate DATETIME2 NULL;

ALTER TABLE DebtPayments
ADD UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 4. REMAININGDEBT TABLOSU - Nullable düzeltmeleri ve eksik alanlar
ALTER TABLE RemainingDebts
ALTER COLUMN IsActive BIT NULL;

-- CreationDate için önce default constraint'i bul ve sil
DECLARE @ConstraintName2 NVARCHAR(200)
SELECT @ConstraintName2 = name FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('RemainingDebts')
AND parent_column_id = (SELECT column_id FROM sys.columns
                        WHERE object_id = OBJECT_ID('RemainingDebts')
                        AND name = 'CreationDate')

IF @ConstraintName2 IS NOT NULL
    EXEC('ALTER TABLE RemainingDebts DROP CONSTRAINT ' + @ConstraintName2)

ALTER TABLE RemainingDebts
ALTER COLUMN CreationDate DATETIME2 NULL;

ALTER TABLE RemainingDebts
ADD UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 5. WORKOUTPROGRAMDAY TABLOSU - Eksik alanlar ekleniyor
ALTER TABLE WorkoutProgramDays
ADD IsActive BIT NULL,
    UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 6. WORKOUTPROGRAMEXERCISE TABLOSU - Eksik alanlar ekleniyor
ALTER TABLE WorkoutProgramExercises
ADD IsActive BIT NULL,
    UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 7. MEMBERSHIPFREEZEHISTORY TABLOSU - Nullable düzeltmeleri ve eksik alanlar
-- CreationDate için önce default constraint'i bul ve sil
DECLARE @ConstraintName3 NVARCHAR(200)
SELECT @ConstraintName3 = name FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('MembershipFreezeHistory')
AND parent_column_id = (SELECT column_id FROM sys.columns
                        WHERE object_id = OBJECT_ID('MembershipFreezeHistory')
                        AND name = 'CreationDate')

IF @ConstraintName3 IS NOT NULL
    EXEC('ALTER TABLE MembershipFreezeHistory DROP CONSTRAINT ' + @ConstraintName3)

ALTER TABLE MembershipFreezeHistory
ALTER COLUMN CreationDate DATETIME2 NULL;

ALTER TABLE MembershipFreezeHistory
ADD IsActive BIT NULL,
    UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 8. USERDEVICE TABLOSU - Nullable düzeltmeleri ve eksik alanlar
ALTER TABLE UserDevices
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE UserDevices
ADD UpdatedDate DATETIME2 NULL,
    DeletedDate DATETIME2 NULL;

-- 9. USER TABLOSU - Nullable düzeltmesi
ALTER TABLE Users
ALTER COLUMN IsActive BIT NULL;

-- 10. EXPENSE TABLOSU - Nullable düzeltmeleri
ALTER TABLE Expenses
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE Expenses
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 11. PRODUCT TABLOSU - Nullable düzeltmeleri
ALTER TABLE Products
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE Products
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 12. TRANSACTION TABLOSU - Nullable düzeltmeleri
ALTER TABLE Transactions
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE Transactions
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 13. LICENSEPACKAGE TABLOSU - Nullable düzeltmeleri
ALTER TABLE LicensePackages
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE LicensePackages
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 14. LICENSETRANSACTION TABLOSU - Nullable düzeltmeleri
ALTER TABLE LicenseTransactions
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE LicenseTransactions
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 15. USERLICENSE TABLOSU - Nullable düzeltmeleri
ALTER TABLE UserLicenses
ALTER COLUMN IsActive BIT NULL;

ALTER TABLE UserLicenses
ALTER COLUMN CreationDate DATETIME2 NULL;

-- 16. MEMBERWORKOUTPROGRAM TABLOSU - Nullable düzeltmesi
ALTER TABLE MemberWorkoutPrograms
ALTER COLUMN IsActive BIT NULL;

-- =====================================================
-- MİGRATİON TAMAMLANDI
-- Toplam 16 tablo güncellendi
-- =====================================================

PRINT 'Migration başarıyla tamamlandı! Tüm tablolar standart soft delete yapısına kavuşturuldu.';
